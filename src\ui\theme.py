"""
Modern Theme Management for ArchPDF - Professional design system implementation.
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
from typing import Dict, Any, Tuple, Optional
from app.config import DARK_THEME, TYPOGRAPHY, SPACING, RADIUS, COMPONENT_SIZES

class ModernThemeManager:
    """Advanced theme manager with modern design system support."""

    def __init__(self, root: ctk.CTk):
        """
        Initialize modern theme manager.

        Args:
            root: Main application window
        """
        self.root = root
        self.current_theme = "dark"
        self.colors = DARK_THEME.copy()
        self.typography = TYPOGRAPHY.copy()
        self.spacing = SPACING.copy()
        self.radius = RADIUS.copy()
        self.sizes = COMPONENT_SIZES.copy()

        # Configure CustomTkinter with modern theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Apply modern design system
        self._configure_modern_theme()
        self._create_component_styles()
    
    def _configure_modern_theme(self):
        """Configure CustomTkinter with modern design system."""
        # Main application colors
        ctk.ThemeManager.theme["CTk"]["fg_color"] = [self.colors["bg_primary"], self.colors["bg_primary"]]

        # Frame colors with elevation
        ctk.ThemeManager.theme["CTkFrame"]["fg_color"] = [self.colors["surface_primary"], self.colors["surface_primary"]]
        ctk.ThemeManager.theme["CTkFrame"]["border_color"] = [self.colors["border_primary"], self.colors["border_primary"]]
        ctk.ThemeManager.theme["CTkFrame"]["border_width"] = 1

        # Neutral gray button styling - minimalist design
        ctk.ThemeManager.theme["CTkButton"]["fg_color"] = [self.colors["surface_tertiary"], self.colors["surface_tertiary"]]
        ctk.ThemeManager.theme["CTkButton"]["hover_color"] = [self.colors["surface_hover"], self.colors["surface_hover"]]
        ctk.ThemeManager.theme["CTkButton"]["text_color"] = [self.colors["text_primary"], self.colors["text_primary"]]
        ctk.ThemeManager.theme["CTkButton"]["border_width"] = 0
        ctk.ThemeManager.theme["CTkButton"]["corner_radius"] = self.radius["sm"]

        # Entry fields with modern styling
        ctk.ThemeManager.theme["CTkEntry"]["fg_color"] = [self.colors["bg_quaternary"], self.colors["bg_quaternary"]]
        ctk.ThemeManager.theme["CTkEntry"]["border_color"] = [self.colors["border_primary"], self.colors["border_primary"]]
        ctk.ThemeManager.theme["CTkEntry"]["text_color"] = [self.colors["text_primary"], self.colors["text_primary"]]
        ctk.ThemeManager.theme["CTkEntry"]["placeholder_text_color"] = [self.colors["text_placeholder"], self.colors["text_placeholder"]]

        # Label styling
        ctk.ThemeManager.theme["CTkLabel"]["text_color"] = [self.colors["text_primary"], self.colors["text_primary"]]

        # Scrollable frame
        ctk.ThemeManager.theme["CTkScrollableFrame"]["fg_color"] = [self.colors["surface_secondary"], self.colors["surface_secondary"]]

    def _create_component_styles(self):
        """Create modern component style definitions."""
        self.component_styles = {
            # === BUTTON STYLES - NEUTRAL GRAY PALETTE ===
            "button_primary": {
                "fg_color": self.colors["surface_tertiary"],
                "hover_color": self.colors["surface_hover"],
                "text_color": self.colors["text_primary"],
                "border_width": 0,
                "corner_radius": self.radius["sm"],
                "font": ("Segoe UI", 12, "normal"),
                "height": self.sizes["button_height_md"],
            },
            "button_secondary": {
                "fg_color": self.colors["surface_secondary"],
                "hover_color": self.colors["surface_hover"],
                "text_color": self.colors["text_secondary"],
                "border_width": 0,
                "border_color": self.colors["border_primary"],
                "corner_radius": self.radius["sm"],
                "font": ("Segoe UI", 12, "normal"),
                "height": self.sizes["button_height_md"],
            },
            "button_ghost": {
                "fg_color": "transparent",
                "hover_color": self.colors["surface_hover"],
                "text_color": self.colors["text_secondary"],
                "border_width": 0,
                "corner_radius": self.radius["sm"],
                "font": ("Segoe UI", 12, "normal"),
                "height": self.sizes["button_height_md"],
            },
            "button_icon": {
                "fg_color": "transparent",
                "hover_color": self.colors["surface_hover"],
                "text_color": self.colors["text_secondary"],
                "border_width": 0,
                "corner_radius": self.radius["sm"],
                "width": 32,
                "height": 32,
            },

            # === FRAME STYLES - MINIMALIST DESIGN ===
            "frame_primary": {
                "fg_color": self.colors["surface_primary"],
                "border_width": 0,
                "corner_radius": 0,
            },
            "frame_secondary": {
                "fg_color": self.colors["surface_secondary"],
                "border_width": 0,
                "border_color": self.colors["border_primary"],
                "corner_radius": 0,
            },
            "frame_elevated": {
                "fg_color": self.colors["bg_elevated"],
                "border_width": 0,
                "border_color": self.colors["border_secondary"],
                "corner_radius": self.radius["sm"],
            },
            "frame_card": {
                "fg_color": self.colors["surface_secondary"],
                "border_width": 0,
                "border_color": self.colors["border_primary"],
                "corner_radius": self.radius["sm"],
            },

            # === LABEL STYLES ===
            "label_primary": {
                "text_color": self.colors["text_primary"],
                "font": ("Segoe UI", 12, "normal"),
            },
            "label_secondary": {
                "text_color": self.colors["text_secondary"],
                "font": ("Segoe UI", 11, "normal"),
            },
            "label_heading": {
                "text_color": self.colors["text_primary"],
                "font": ("Segoe UI", 14, "bold"),
            },
            "label_caption": {
                "text_color": self.colors["text_tertiary"],
                "font": ("Segoe UI", 10, "normal"),
            },
        }

    # === UTILITY METHODS ===

    def get_color(self, color_name: str) -> str:
        """Get color value by name."""
        return self.colors.get(color_name, "#ffffff")

    def get_font(self, style: str = "primary", size: str = "base", weight: str = "normal") -> Tuple[str, int, str]:
        """Get font tuple for tkinter widgets."""
        # Get font family
        font_families = self.typography.get(f"font_{style}", self.typography["font_primary"])
        font_family = font_families[0] if isinstance(font_families, tuple) else font_families

        # Get font size
        font_size = self.typography.get(f"size_{size}", self.typography["size_base"])

        # Get font weight
        font_weight = self.typography.get(f"weight_{weight}", "normal")

        return (font_family, font_size, font_weight)

    def get_spacing(self, size: str) -> int:
        """Get spacing value by size name."""
        return self.spacing.get(size, 8)

    def get_radius(self, size: str) -> int:
        """Get border radius by size name."""
        return self.radius.get(size, 6)

    def get_component_style(self, component: str) -> Dict[str, Any]:
        """Get complete component style dictionary."""
        return self.component_styles.get(component, {}).copy()

    # === COMPONENT STYLE GETTERS ===

    def get_button_style(self, variant: str = "primary") -> Dict[str, Any]:
        """Get button style by variant."""
        return self.get_component_style(f"button_{variant}")

    def get_frame_style(self, variant: str = "primary") -> Dict[str, Any]:
        """Get frame style by variant."""
        return self.get_component_style(f"frame_{variant}")

    def get_label_style(self, variant: str = "primary") -> Dict[str, Any]:
        """Get label style by variant."""
        return self.get_component_style(f"label_{variant}")

    # === ADVANCED STYLING METHODS ===

    def create_modern_button(self, parent, text: str, command=None, variant: str = "primary", **kwargs) -> ctk.CTkButton:
        """Create a modern styled button."""
        style = self.get_button_style(variant)
        style.update(kwargs)  # Allow override

        return ctk.CTkButton(
            parent,
            text=text,
            command=command,
            **style
        )

    def create_modern_frame(self, parent, variant: str = "primary", **kwargs) -> ctk.CTkFrame:
        """Create a modern styled frame."""
        style = self.get_frame_style(variant)
        style.update(kwargs)  # Allow override

        return ctk.CTkFrame(parent, **style)

    def create_modern_label(self, parent, text: str, variant: str = "primary", **kwargs) -> ctk.CTkLabel:
        """Create a modern styled label."""
        style = self.get_label_style(variant)
        style.update(kwargs)  # Allow override

        return ctk.CTkLabel(parent, text=text, **style)

    def create_separator(self, parent, orientation: str = "horizontal") -> ctk.CTkFrame:
        """Create a modern separator line."""
        if orientation == "horizontal":
            return ctk.CTkFrame(
                parent,
                height=1,
                fg_color=self.colors["border_primary"],
                corner_radius=0
            )
        else:
            return ctk.CTkFrame(
                parent,
                width=1,
                fg_color=self.colors["border_primary"],
                corner_radius=0
            )

    def apply_modern_theme(self):
        """Apply the modern theme to the application."""
        self.current_theme = "dark"

        # Set root window colors
        self.root.configure(fg_color=self.colors["bg_primary"])

        # Update CustomTkinter appearance
        ctk.set_appearance_mode("dark")

        # Apply to all existing widgets
        self._apply_theme_to_widgets(self.root)

    def _apply_theme_to_widgets(self, parent):
        """Recursively apply modern theme to all widgets."""
        for child in parent.winfo_children():
            # Apply theme based on widget type
            if isinstance(child, ctk.CTkFrame):
                child.configure(fg_color=self.colors["surface_primary"])
            elif isinstance(child, ctk.CTkButton):
                child.configure(
                    fg_color=self.colors["interactive_primary"],
                    hover_color=self.colors["interactive_primary_hover"],
                    text_color=self.colors["text_primary"]
                )
            elif isinstance(child, ctk.CTkLabel):
                child.configure(text_color=self.colors["text_primary"])
            elif isinstance(child, ctk.CTkEntry):
                child.configure(
                    fg_color=self.colors["bg_quaternary"],
                    border_color=self.colors["border_primary"],
                    text_color=self.colors["text_primary"]
                )

            # Recursively apply to children
            self._apply_theme_to_widgets(child)

    def apply_hover_effect(self, widget, enter_color: str = None, leave_color: str = None):
        """
        Apply hover effect to a widget.

        Args:
            widget: Widget to apply hover effect to
            enter_color: Color when mouse enters
            leave_color: Color when mouse leaves
        """
        if enter_color is None:
            enter_color = self.colors["surface_hover"]
        if leave_color is None:
            leave_color = self.colors["surface_primary"]

        def on_enter(event):
            widget.configure(fg_color=enter_color)

        def on_leave(event):
            widget.configure(fg_color=leave_color)

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)


# Alias for backward compatibility
ThemeManager = ModernThemeManager
