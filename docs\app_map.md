# ArchPDF - Mappa dell'Applicazione

## Panoramica del Progetto
**ArchPDF** è un lettore PDF professionale per Windows con tema scuro moderno e icone bicolore.

### Informazioni Generali
- **Nome**: ArchPDF
- **Versione**: 1.0.0
- **Autore**: Augment Agent
- **Linguaggio**: Python 3.x
- **Framework GUI**: CustomTkinter + Tkinter
- **Motore PDF**: PyMuPDF (fitz)
- **Piattaforma**: Windows

## Struttura delle Cartelle

```
archpdf_v2/
├── main.py                 # Entry point principale dell'applicazione
├── requirements.txt        # Dipendenze Python
├── ArchPDF.spec           # Configurazione PyInstaller
├── build_exe.py           # Script per build eseguibile
├── create_app_icon.py     # Script per creazione icone
├── run_tests.py           # Runner per i test
├── test_ui.py             # Test interfaccia utente
│
├── docs/                  # Documentazione
│   └── app_map.md         # Questo file - mappa dell'app
│
├── assets/                # Risorse statiche
│   └── icons/             # Icone dell'applicazione
│
├── src/                   # Codice sorgente principale
│   ├── __init__.py
│   ├── app/               # Logica applicazione principale
│   │   ├── __init__.py
│   │   ├── config.py      # Configurazioni e costanti
│   │   ├── main_window.py # Finestra principale
│   │   └── settings.py    # Gestione impostazioni
│   │
│   ├── pdf/               # Motore PDF e funzionalità correlate
│   │   ├── __init__.py
│   │   ├── engine.py      # Motore PDF core (PyMuPDF)
│   │   ├── search.py      # Funzionalità di ricerca
│   │   └── viewer.py      # Visualizzatore PDF
│   │
│   ├── ui/                # Componenti interfaccia utente
│   │   ├── __init__.py
│   │   ├── menubar.py     # Barra menu
│   │   ├── toolbar.py     # Barra strumenti
│   │   ├── statusbar.py   # Barra stato
│   │   ├── sidebar.py     # Pannello laterale
│   │   ├── theme.py       # Gestione temi
│   │   └── search_dialog.py # Dialog di ricerca
│   │
│   └── utils/             # Utilità e helper
│       ├── __init__.py
│       ├── file_manager.py    # Gestione file
│       ├── settings_manager.py # Gestione impostazioni
│       ├── icon_manager.py    # Gestione icone
│       └── error_handler.py   # Gestione errori
│
├── tests/                 # Test unitari
│   ├── __init__.py
│   ├── test_file_manager.py
│   └── test_pdf_engine.py
│
├── build/                 # File di build
└── dist/                  # Distribuzione finale
```

## Componenti Principali

### 1. Entry Point (main.py)
- Punto di ingresso dell'applicazione
- Gestisce l'importazione dei moduli
- Avvia l'applicazione principale

### 2. Applicazione Core (src/app/)
- **main_window.py**: Finestra principale con layout e gestione eventi
- **config.py**: Configurazioni, costanti e tema scuro
- **settings.py**: Gestione persistenza impostazioni

### 3. Motore PDF (src/pdf/)
- **engine.py**: Core PDF usando PyMuPDF per rendering e manipolazione
- **search.py**: Funzionalità di ricerca nel testo
- **viewer.py**: Componente visualizzazione PDF

### 4. Interfaccia Utente (src/ui/)
- **menubar.py**: Menu principale dell'applicazione
- **toolbar.py**: Barra strumenti con controlli rapidi
- **statusbar.py**: Barra stato con informazioni documento
- **sidebar.py**: Pannello laterale con bookmarks e navigazione
- **theme.py**: Gestione tema scuro e colori
- **search_dialog.py**: Dialog per ricerca testo

### 5. Utilità (src/utils/)
- **file_manager.py**: Gestione file e file recenti
- **settings_manager.py**: Persistenza configurazioni
- **icon_manager.py**: Gestione icone bicolore
- **error_handler.py**: Gestione errori e dialog

## Dipendenze Principali

### GUI Framework
- **customtkinter**: Framework GUI moderno
- **tkinter**: Framework GUI base Python

### PDF Processing
- **PyMuPDF (fitz)**: Motore PDF per rendering e manipolazione

### Image Processing
- **Pillow**: Elaborazione immagini

### Packaging
- **pyinstaller**: Creazione eseguibile standalone

## Funzionalità Implementate

### Core Features
- ✅ Apertura e visualizzazione PDF
- ✅ Navigazione pagine (avanti/indietro)
- ✅ Controlli zoom (in/out/fit)
- ✅ Ricerca testo nel documento
- ✅ Bookmarks e navigazione outline
- ✅ File recenti

### UI Features
- ✅ Tema scuro moderno
- ✅ Icone bicolore
- ✅ Sidebar con bookmarks
- ✅ Barra strumenti
- ✅ Barra stato
- ✅ Scorciatoie tastiera

### Advanced Features
- ✅ Gestione errori robusta
- ✅ Progress dialog per file grandi
- ✅ Persistenza impostazioni
- ✅ Fullscreen mode
- ✅ Gestione file manager

## Configurazione

### Impostazioni Default
- Tema: Scuro
- Zoom: 100%
- Modalità fit: Fit Width
- Finestra: 1200x800
- Sidebar: Visibile (250px)

### Colori Tema Scuro
- Background primario: #1e1e1e
- Background secondario: #2d2d2d
- Testo primario: #ffffff
- Accent primario: #007acc (blu)
- Accent secondario: #ff6b35 (arancione)

## File di Configurazione
- Impostazioni salvate in: `~/.archpdf/`
- Formato: JSON
- Auto-save al chiusura applicazione

## Test e Build

### Test
- Test unitari in `/tests/`
- Runner: `run_tests.py`
- Framework: pytest

### Build Eseguibile
- Script: `build_exe.py`
- Tool: PyInstaller
- Configurazione: `ArchPDF.spec`
- Output: `/dist/ArchPDF.exe`

## Note Tecniche

### Architettura
- Pattern MVC con separazione responsabilità
- Gestione eventi centralizzata
- Error handling robusto
- Logging integrato

### Performance
- Rendering lazy delle pagine
- Cache immagini
- Progress dialog per operazioni lunghe
- Gestione memoria ottimizzata

### Sicurezza
- Validazione file PDF
- Gestione eccezioni
- Controllo permessi file

## Aggiornamenti Recenti

### 2025-06-17 - FASE 1 COMPLETATA: UI CORE MODERNIZZATO

#### FASE 1.1 - Viewer Area Premium ✅
- ✅ **Viewer Modernizzato**: Design premium con bordi eleganti, shadows e corner radius
- ✅ **Placeholder Moderno**: Nuovo design con icone Unicode e layout professionale
- ✅ **Loading States**: Indicatori di caricamento per file grandi con threading asincrono
- ✅ **Canvas Migliorato**: Background pattern subtile e shadow effects per PDF
- ✅ **Smooth Scrolling**: Animazioni fluide per lo scrolling del mouse (~60fps)
- ✅ **Styling Avanzato**: Integrazione completa con design system GitHub Dark

#### FASE 1.2 - Menu Contestuali Professionali ✅
- ✅ **Menu Modernizzati**: Icone Unicode moderne per tutti i menu items
- ✅ **GitHub Dark Theme**: Styling consistente con palette colori professionale
- ✅ **Hover Effects**: Transizioni fluide e feedback visuale
- ✅ **Recent Files**: Numerazione per accesso rapido e icone descrittive
- ✅ **Keyboard Shortcuts**: Visibili nei menu per migliore usabilità

#### FASE 1.3 - Layout Ottimizzato ✅
- ✅ **Spacing Consistente**: Sistema di spacing standardizzato dal theme manager
- ✅ **Grid Layout**: Layout responsive con grid system moderno
- ✅ **Responsive Design**: Auto-hide sidebar su schermi piccoli (<1000px)
- ✅ **Window Resizing**: Gestione intelligente del resize con minimum size
- ✅ **Border Styling**: Corner radius e border consistenti per tutti i componenti

### Modifiche Tecniche FASE 1
- **File Modificati**:
  - `src/pdf/viewer.py` - Viewer premium con loading states
  - `src/ui/menubar.py` - Menu moderni con icone e GitHub Dark theme
  - `src/app/main_window.py` - Layout responsive e spacing ottimizzato
- **Nuovi Metodi**:
  - `_create_modern_placeholder()`, `_create_loading_indicator()`, `_smooth_scroll()`
  - `_get_modern_menu_style()`, `_on_window_resize()`
- **Threading**: Caricamento asincrono per file PDF grandi
- **Responsive**: Auto-layout basato su dimensioni finestra

## Prossimi Sviluppi
- [ ] Menu contestuali professionali (FASE 1.2)
- [ ] Ottimizzazione layout finale (FASE 1.3)
- [ ] Search dialog di nuova generazione (FASE 2.1)
- [ ] Sistema icone vettoriali (FASE 2.2)
- [ ] Sistema thumbnails avanzato (FASE 2.3)
- [ ] Annotazioni PDF
- [ ] Stampa documenti
- [ ] Esportazione immagini
- [ ] Plugin system
- [ ] Multi-tab support

---
*Ultimo aggiornamento: 2025-06-17*
*Versione documento: 1.1*
