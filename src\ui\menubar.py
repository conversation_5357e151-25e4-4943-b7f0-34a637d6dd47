"""
Modern Menu Bar Component for ArchPDF - Professional menu system with GitHub Dark theme.
File: src/ui/menubar.py
"""

import tkinter as tk
from tkinter import messagebox
import logging

logger = logging.getLogger(__name__)

class MenuBar:
    """Modern application menu bar with GitHub Dark theme and professional styling."""

    def __init__(self, root, app):
        """
        Initialize the modern menu bar.

        Args:
            root: Root window
            app: Main application instance
        """
        self.root = root
        self.app = app

        # Modern menu icons (Unicode symbols)
        self.icons = {
            # File menu icons
            "open": "📂",
            "recent": "🕒",
            "close": "✖️",
            "exit": "🚪",

            # View menu icons
            "zoom_in": "🔍",
            "zoom_out": "🔎",
            "actual_size": "📏",
            "fit_width": "↔️",
            "fit_page": "📄",
            "next_page": "▶️",
            "prev_page": "◀️",
            "goto": "🎯",
            "sidebar": "📋",
            "fullscreen": "⛶",

            # Tools menu icons
            "search": "🔍",
            "properties": "ℹ️",
            "settings": "⚙️",

            # Help menu icons
            "shortcuts": "⌨️",
            "about": "❓"
        }

        # Create modern menu bar with GitHub Dark styling
        self.menubar = tk.Menu(
            root,
            bg=self.app.theme_manager.get_color("surface_primary"),
            fg=self.app.theme_manager.get_color("text_primary"),
            activebackground=self.app.theme_manager.get_color("interactive_primary"),
            activeforeground=self.app.theme_manager.get_color("text_primary"),
            selectcolor=self.app.theme_manager.get_color("interactive_primary"),
            relief="flat",
            borderwidth=0,
            font=self.app.theme_manager.get_font("primary", "base", "normal")
        )

        # Create modern menus
        self._create_file_menu()
        self._create_view_menu()
        self._create_tools_menu()
        self._create_help_menu()

        # Set menu bar
        root.config(menu=self.menubar)

        # Initial state
        self.update_state(False)

    def _get_modern_menu_style(self) -> dict:
        """Get consistent modern styling for all menus."""
        return {
            "tearoff": 0,
            "bg": self.app.theme_manager.get_color("surface_primary"),
            "fg": self.app.theme_manager.get_color("text_primary"),
            "activebackground": self.app.theme_manager.get_color("interactive_primary"),
            "activeforeground": self.app.theme_manager.get_color("text_primary"),
            "selectcolor": self.app.theme_manager.get_color("interactive_primary"),
            "relief": "flat",
            "borderwidth": 1,
            "font": self.app.theme_manager.get_font("primary", "sm", "normal")
        }
    
    def _create_file_menu(self):
        """Create modern File menu with icons and professional styling."""
        self.file_menu = tk.Menu(self.menubar, **self._get_modern_menu_style())

        # Add menu items with modern icons
        self.file_menu.add_command(
            label=f"{self.icons['open']}  Open...",
            command=self.app.open_file,
            accelerator="Ctrl+O"
        )

        # Modern separator
        self.file_menu.add_separator()

        # Recent files submenu with modern styling
        self.recent_menu = tk.Menu(self.file_menu, **self._get_modern_menu_style())
        self.file_menu.add_cascade(
            label=f"{self.icons['recent']}  Recent Files",
            menu=self.recent_menu
        )
        self._update_recent_files()

        # More menu items with icons
        self.file_menu.add_separator()
        self.file_menu.add_command(
            label=f"{self.icons['close']}  Close",
            command=self.app.close_file,
            state="disabled"
        )

        self.file_menu.add_separator()
        self.file_menu.add_command(
            label=f"{self.icons['exit']}  Exit",
            command=self.app.on_closing,
            accelerator="Ctrl+Q"
        )

        # Add to menubar with modern styling
        self.menubar.add_cascade(label="📁 File", menu=self.file_menu)
    
    def _create_view_menu(self):
        """Create modern View menu with icons and professional styling."""
        self.view_menu = tk.Menu(self.menubar, **self._get_modern_menu_style())

        # Modern Zoom submenu
        self.zoom_menu = tk.Menu(self.view_menu, **self._get_modern_menu_style())

        self.zoom_menu.add_command(
            label=f"{self.icons['zoom_in']}  Zoom In",
            command=self.app.zoom_in,
            accelerator="Ctrl++",
            state="disabled"
        )
        self.zoom_menu.add_command(
            label=f"{self.icons['zoom_out']}  Zoom Out",
            command=self.app.zoom_out,
            accelerator="Ctrl+-",
            state="disabled"
        )
        self.zoom_menu.add_command(
            label=f"{self.icons['actual_size']}  Actual Size",
            command=self.app.zoom_actual_size,
            accelerator="Ctrl+0",
            state="disabled"
        )

        self.zoom_menu.add_separator()

        self.zoom_menu.add_command(
            label=f"{self.icons['fit_width']}  Fit Width",
            command=self.app.zoom_fit_width,
            state="disabled"
        )
        self.zoom_menu.add_command(
            label=f"{self.icons['fit_page']}  Fit Page",
            command=self.app.zoom_fit_page,
            state="disabled"
        )

        self.view_menu.add_cascade(label="🔍 Zoom", menu=self.zoom_menu)
        self.view_menu.add_separator()

        # Modern Navigation section
        self.view_menu.add_command(
            label=f"{self.icons['next_page']}  Next Page",
            command=self.app.next_page,
            accelerator="Page Down",
            state="disabled"
        )
        self.view_menu.add_command(
            label=f"{self.icons['prev_page']}  Previous Page",
            command=self.app.previous_page,
            accelerator="Page Up",
            state="disabled"
        )
        self.view_menu.add_command(
            label=f"{self.icons['goto']}  Go to Page...",
            command=self._show_goto_dialog,
            state="disabled"
        )

        self.view_menu.add_separator()

        # Modern View options
        self.view_menu.add_command(
            label=f"{self.icons['sidebar']}  Toggle Sidebar",
            command=self.app.toggle_sidebar
        )
        self.view_menu.add_command(
            label=f"{self.icons['fullscreen']}  Fullscreen",
            command=self.app.toggle_fullscreen,
            accelerator="F11"
        )

        self.menubar.add_cascade(label="👁️ View", menu=self.view_menu)
    
    def _create_tools_menu(self):
        """Create modern Tools menu with icons and professional styling."""
        self.tools_menu = tk.Menu(self.menubar, **self._get_modern_menu_style())

        self.tools_menu.add_command(
            label=f"{self.icons['search']}  Find...",
            command=self.app.show_search_dialog,
            accelerator="Ctrl+F",
            state="disabled"
        )

        self.tools_menu.add_separator()

        self.tools_menu.add_command(
            label=f"{self.icons['properties']}  Document Properties",
            command=self._show_properties,
            state="disabled"
        )

        self.tools_menu.add_separator()

        self.tools_menu.add_command(
            label=f"{self.icons['settings']}  Settings...",
            command=self._show_settings
        )

        self.menubar.add_cascade(label="🔧 Tools", menu=self.tools_menu)

    def _create_help_menu(self):
        """Create modern Help menu with icons and professional styling."""
        self.help_menu = tk.Menu(self.menubar, **self._get_modern_menu_style())

        self.help_menu.add_command(
            label=f"{self.icons['shortcuts']}  Keyboard Shortcuts",
            command=self._show_shortcuts
        )

        self.help_menu.add_separator()

        self.help_menu.add_command(
            label=f"{self.icons['about']}  About ArchPDF",
            command=self.app.show_about
        )

        self.menubar.add_cascade(label="❓ Help", menu=self.help_menu)
    
    def _update_recent_files(self):
        """Update recent files menu with modern styling and icons."""
        # Clear existing items
        self.recent_menu.delete(0, "end")

        # Get recent files
        recent_files = self.app.file_manager.get_recent_files()

        if recent_files:
            for i, file_path in enumerate(recent_files):
                filename = file_path.split("/")[-1] if "/" in file_path else file_path.split("\\")[-1]
                # Add number prefix for quick access
                self.recent_menu.add_command(
                    label=f"📄  {i+1}. {filename}",
                    command=lambda path=file_path: self.app.open_file(path)
                )

            self.recent_menu.add_separator()
            self.recent_menu.add_command(
                label="🗑️  Clear Recent Files",
                command=self.app.file_manager.clear_recent_files
            )
        else:
            self.recent_menu.add_command(
                label="📭  (No recent files)",
                state="disabled"
            )
    
    def _show_goto_dialog(self):
        """Show go to page dialog."""
        if not self.app.pdf_engine.is_document_loaded():
            return
        
        # Simple input dialog
        from tkinter import simpledialog
        page_num = simpledialog.askinteger(
            "Go to Page",
            f"Enter page number (1-{self.app.pdf_engine.page_count}):",
            minvalue=1,
            maxvalue=self.app.pdf_engine.page_count
        )
        
        if page_num:
            self.app.go_to_page(page_num)
    
    def _show_properties(self):
        """Show document properties dialog."""
        if not self.app.pdf_engine.is_document_loaded():
            return
        
        doc_info = self.app.pdf_engine.get_document_info()
        
        properties_text = f"""Document Properties

Title: {doc_info.get('title', 'N/A')}
Author: {doc_info.get('author', 'N/A')}
Subject: {doc_info.get('subject', 'N/A')}
Creator: {doc_info.get('creator', 'N/A')}
Producer: {doc_info.get('producer', 'N/A')}

Pages: {doc_info.get('page_count', 0)}
File Size: {doc_info.get('file_size', 0):,} bytes

Created: {doc_info.get('creation_date', 'N/A')}
Modified: {doc_info.get('modification_date', 'N/A')}"""
        
        messagebox.showinfo("Document Properties", properties_text)

    def _show_settings(self):
        """Show settings dialog."""
        from app.settings import SettingsDialog
        SettingsDialog(self.root, self.app)
    
    def _show_shortcuts(self):
        """Show keyboard shortcuts dialog."""
        shortcuts_text = """Keyboard Shortcuts

File Operations:
Ctrl+O          Open file
Ctrl+Q          Quit application

Navigation:
Page Up         Previous page
Page Down       Next page

Zoom:
Ctrl++          Zoom in
Ctrl+-          Zoom out
Ctrl+0          Actual size

Search:
Ctrl+F          Find text

View:
F11             Toggle fullscreen"""
        
        messagebox.showinfo("Keyboard Shortcuts", shortcuts_text)
    
    def update_state(self, has_document: bool):
        """
        Update menu state based on document availability with modern menu items.

        Args:
            has_document: Whether a document is loaded
        """
        # File menu - use new icon-based labels
        self.file_menu.entryconfig(f"{self.icons['close']}  Close",
                                  state="normal" if has_document else "disabled")

        # View menu - zoom items (update all items by index for safety)
        state = "normal" if has_document else "disabled"
        for i in range(self.zoom_menu.index("end") + 1):
            try:
                self.zoom_menu.entryconfig(i, state=state)
            except:
                pass

        # View menu - navigation items with new icon-based labels
        self.view_menu.entryconfig(f"{self.icons['next_page']}  Next Page", state=state)
        self.view_menu.entryconfig(f"{self.icons['prev_page']}  Previous Page", state=state)
        self.view_menu.entryconfig(f"{self.icons['goto']}  Go to Page...", state=state)

        # Tools menu - use new icon-based labels
        self.tools_menu.entryconfig(f"{self.icons['search']}  Find...", state=state)
        self.tools_menu.entryconfig(f"{self.icons['properties']}  Document Properties", state=state)

        # Update recent files
        self._update_recent_files()
